"""
Speech-to-Text (STT) providers for Webscout.

This module provides various STT providers with OpenAI Whisper API compatibility.
All providers return transcription results in a standardized format that matches
the OpenAI Whisper API response structure.

Available Providers:
- ElevenLabsSTT: Uses ElevenLabs API for speech-to-text
- OpenAIWhisperSTT: Uses OpenAI Whisper API or compatible endpoints
- AsyncElevenLabsSTT: Async version of ElevenLabs provider
- AsyncOpenAIWhisperSTT: Async version of OpenAI Whisper provider
- STTClient: OpenAI-compatible client interface

Example Usage:
    # Traditional provider usage
    from webscout.Provider.STT import ElevenLabsSTT

    stt = ElevenLabsSTT()
    result = stt.transcribe("audio.mp3")
    print(result["text"])

    # OpenAI-compatible client usage
    from webscout.Provider.STT import STTClient

    client = STTClient()
    audio_file = open("/path/to/file/audio.mp3", "rb")
    transcription = client.audio.transcriptions.create(
        model="whisper-1",
        file=audio_file,
        response_format="text"
    )
    print(transcription.text)
"""

from .base import *
from .elevenlabs import *
from .openai_client import *
from . import utils
