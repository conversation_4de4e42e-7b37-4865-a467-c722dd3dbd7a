#!/usr/bin/env python3
"""
Example usage of the OpenAI-compatible STT client in Webscout.

This example demonstrates how to use the STTClient which provides
an interface identical to OpenAI's Whisper API.
"""

import sys
from pathlib import Path

# Add the parent directory to the path to import webscout
sys.path.insert(0, str(Path(__file__).parent.parent))

def basic_transcription_example():
    """Basic transcription example using the OpenAI-compatible interface."""
    print("🎵 Basic Transcription Example")
    print("=" * 50)
    
    try:
        from webscout.client import STTClient
        
        # Initialize the client
        client = STTClient()
        
        # Example audio file path (you would replace this with your actual file)
        audio_file_path = "path/to/your/audio.mp3"
        
        print(f"📋 Available models: {client.AVAILABLE_MODELS}")
        
        # Note: Since we don't have an actual audio file, we'll show the interface
        print("\n📝 Example code for basic transcription:")
        print("""
# Open your audio file
audio_file = open("path/to/your/audio.mp3", "rb")

# Transcribe with simple text output
transcription = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_file,
    response_format="text"
)

print(transcription.text)
        """)
        
    except Exception as e:
        print(f"❌ Error: {e}")

def advanced_transcription_example():
    """Advanced transcription example with all parameters."""
    print("\n🔧 Advanced Transcription Example")
    print("=" * 50)
    
    try:
        from webscout.client import STTClient
        
        client = STTClient()
        
        print("📝 Example code for advanced transcription:")
        print("""
# Advanced transcription with all parameters
audio_file = open("path/to/your/audio.mp3", "rb")

transcription = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_file,
    language="en",  # ISO-639-1 language code
    prompt="This is a conversation about technology.",  # Context prompt
    response_format="verbose_json",  # Get detailed response
    temperature=0.2,  # Lower temperature for more consistent results
    timestamp_granularities=["segment", "word"]  # Include word-level timestamps
)

# Access the transcription text
print(f"Text: {transcription.text}")

# Access metadata
print(f"Language: {transcription.language}")
print(f"Duration: {transcription.duration} seconds")

# Access segments with timestamps
for segment in transcription.segments:
    start = segment['start']
    end = segment['end']
    text = segment['text']
    print(f"[{start:.2f}s - {end:.2f}s]: {text}")

# Access word-level timestamps (if available)
if transcription.words:
    for word in transcription.words[:5]:  # Show first 5 words
        print(f"Word: '{word['word']}' at {word['start']:.2f}s")
        """)
        
    except Exception as e:
        print(f"❌ Error: {e}")

def response_formats_example():
    """Example showing different response formats."""
    print("\n📄 Response Formats Example")
    print("=" * 50)
    
    try:
        from webscout.client import STTClient
        
        client = STTClient()
        
        print("📝 Example code for different response formats:")
        print("""
audio_file = open("path/to/your/audio.mp3", "rb")

# 1. Text format (just the transcribed text)
text_response = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_file,
    response_format="text"
)
print("Text format:", text_response.text)

# 2. JSON format (basic metadata)
json_response = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_file,
    response_format="json"
)
print("JSON format:", str(json_response))

# 3. Verbose JSON format (detailed metadata with segments)
verbose_response = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_file,
    response_format="verbose_json"
)
print("Verbose JSON format:", str(verbose_response))

# 4. SRT subtitle format
srt_response = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_file,
    response_format="srt"
)
print("SRT format:")
print(str(srt_response))

# 5. VTT subtitle format
vtt_response = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_file,
    response_format="vtt"
)
print("VTT format:")
print(str(vtt_response))
        """)
        
    except Exception as e:
        print(f"❌ Error: {e}")

def file_handling_example():
    """Example showing different ways to handle audio files."""
    print("\n📁 File Handling Example")
    print("=" * 50)
    
    try:
        from webscout.client import STTClient
        
        client = STTClient()
        
        print("📝 Example code for different file handling methods:")
        print("""
# Method 1: Using file path directly
transcription1 = client.audio.transcriptions.create(
    model="whisper-1",
    file="path/to/your/audio.mp3",
    response_format="text"
)

# Method 2: Using file object
with open("path/to/your/audio.mp3", "rb") as audio_file:
    transcription2 = client.audio.transcriptions.create(
        model="whisper-1",
        file=audio_file,
        response_format="text"
    )

# Method 3: Using Path object
from pathlib import Path
audio_path = Path("path/to/your/audio.mp3")
transcription3 = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_path,
    response_format="text"
)

# Method 4: Using BytesIO (for in-memory audio data)
import io
# Assuming you have audio_bytes from somewhere
audio_stream = io.BytesIO(audio_bytes)
transcription4 = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_stream,
    response_format="text"
)
        """)
        
    except Exception as e:
        print(f"❌ Error: {e}")

def migration_from_openai_example():
    """Example showing how to migrate from OpenAI Whisper API."""
    print("\n🔄 Migration from OpenAI Example")
    print("=" * 50)
    
    print("📝 Migrating from OpenAI Whisper API is simple:")
    print("""
# Before (OpenAI Whisper API):
import openai

client = openai.OpenAI(api_key="your-openai-api-key")
audio_file = open("audio.mp3", "rb")
transcription = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_file,
    response_format="text"
)
print(transcription.text)

# After (Webscout STT Client):
from webscout.client import STTClient

client = STTClient()  # No API key needed!
audio_file = open("audio.mp3", "rb")
transcription = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_file,
    response_format="text"
)
print(transcription.text)

# The interface is identical! Just change the import and remove the API key.
    """)

def main():
    """Run all examples."""
    print("🎙️  Webscout OpenAI-Compatible STT Client Examples")
    print("=" * 60)
    
    basic_transcription_example()
    advanced_transcription_example()
    response_formats_example()
    file_handling_example()
    migration_from_openai_example()
    
    print("\n" + "=" * 60)
    print("✨ Examples completed!")
    print("\nTo run actual transcriptions, replace the example file paths")
    print("with paths to your actual audio files.")
    print("\nSupported audio formats: MP3, WAV, M4A, MP4, OGG, FLAC, WebM, AAC")

if __name__ == "__main__":
    main()
