#!/usr/bin/env python3
"""
Test script for the OpenAI-compatible STT client.
"""

import sys
import os
from pathlib import Path

# Add the webscout directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_stt_client():
    """Test the STT client with OpenAI-compatible interface."""
    try:
        # Import the STT client
        from webscout.Provider.STT import STTClient
        
        print("✅ Successfully imported STTClient")
        
        # Initialize the client
        client = STTClient()
        print("✅ Successfully initialized STTClient")
        
        # Check available models
        print(f"📋 Available models: {client.AVAILABLE_MODELS}")
        
        # Test the interface structure
        assert hasattr(client, 'audio'), "Client should have 'audio' attribute"
        assert hasattr(client.audio, 'transcriptions'), "Audio should have 'transcriptions' attribute"
        assert hasattr(client.audio.transcriptions, 'create'), "Transcriptions should have 'create' method"
        
        print("✅ Client interface structure is correct")
        
        # Test with a dummy file (if available)
        test_audio_path = "test_audio.mp3"  # You would need to provide this
        
        if os.path.exists(test_audio_path):
            print(f"🎵 Testing with audio file: {test_audio_path}")
            
            # Test with file path
            try:
                with open(test_audio_path, "rb") as audio_file:
                    transcription = client.audio.transcriptions.create(
                        model="whisper-1",
                        file=audio_file,
                        response_format="text"
                    )
                    print(f"📝 Transcription: {transcription.text}")
                    print("✅ File-based transcription successful")
            except Exception as e:
                print(f"❌ File-based transcription failed: {e}")
        else:
            print(f"⚠️  Test audio file '{test_audio_path}' not found, skipping actual transcription test")
        
        print("\n🎉 All tests passed! The STT client is working correctly.")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    return True

def test_client_import():
    """Test importing from webscout.client."""
    try:
        from webscout.client import STTClient
        print("✅ Successfully imported STTClient from webscout.client")
        
        client = STTClient()
        print("✅ Successfully initialized STTClient from webscout.client")
        
        return True
    except ImportError as e:
        print(f"❌ Import from webscout.client failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Client test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing OpenAI-compatible STT Client\n")
    
    print("=" * 50)
    print("Test 1: Direct import from STT module")
    print("=" * 50)
    test1_passed = test_stt_client()
    
    print("\n" + "=" * 50)
    print("Test 2: Import from webscout.client")
    print("=" * 50)
    test2_passed = test_client_import()
    
    print("\n" + "=" * 50)
    print("Summary")
    print("=" * 50)
    
    if test1_passed and test2_passed:
        print("🎉 All tests passed! The STT client is ready to use.")
        print("\nExample usage:")
        print("""
from webscout.client import STTClient

client = STTClient()
audio_file = open("/path/to/file/audio.mp3", "rb")
transcription = client.audio.transcriptions.create(
    model="whisper-1",
    file=audio_file,
    response_format="text"
)
print(transcription.text)
        """)
    else:
        print("❌ Some tests failed. Please check the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
