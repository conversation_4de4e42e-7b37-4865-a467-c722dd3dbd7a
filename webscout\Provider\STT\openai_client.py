"""
OpenAI-compatible STT client for Webscout.

This module provides an OpenAI Whisper API-compatible interface for speech-to-text
transcription using various backend providers.
"""

import io
import json
from pathlib import Path
from typing import Any, Dict, Optional, Union, BinaryIO
from .elevenlabs import ElevenLabsSTT
from .base import BaseSTTProvider


class TranscriptionResponse:
    """Response object that mimics OpenAI's transcription response."""
    
    def __init__(self, data: Dict[str, Any], response_format: str = "json"):
        self._data = data
        self._response_format = response_format
        
    @property
    def text(self) -> str:
        """Get the transcribed text."""
        return self._data.get("text", "")
    
    @property
    def language(self) -> Optional[str]:
        """Get the detected language."""
        return self._data.get("language")
    
    @property
    def duration(self) -> Optional[float]:
        """Get the audio duration."""
        return self._data.get("duration")
    
    @property
    def segments(self) -> Optional[list]:
        """Get the segments with timestamps."""
        return self._data.get("segments")
    
    @property
    def words(self) -> Optional[list]:
        """Get the words with timestamps."""
        return self._data.get("words")
    
    def __str__(self) -> str:
        """Return string representation based on response format."""
        if self._response_format == "text":
            return self.text
        elif self._response_format == "srt":
            return self._to_srt()
        elif self._response_format == "vtt":
            return self._to_vtt()
        else:  # json or verbose_json
            return json.dumps(self._data, indent=2)
    
    def _to_srt(self) -> str:
        """Convert to SRT subtitle format."""
        if not self.segments:
            return ""
        
        srt_content = []
        for i, segment in enumerate(self.segments, 1):
            start_time = self._format_time_srt(segment.get("start", 0))
            end_time = self._format_time_srt(segment.get("end", 0))
            text = segment.get("text", "").strip()
            
            srt_content.append(f"{i}")
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(text)
            srt_content.append("")
        
        return "\n".join(srt_content)
    
    def _to_vtt(self) -> str:
        """Convert to VTT subtitle format."""
        if not self.segments:
            return "WEBVTT\n\n"
        
        vtt_content = ["WEBVTT", ""]
        for segment in self.segments:
            start_time = self._format_time_vtt(segment.get("start", 0))
            end_time = self._format_time_vtt(segment.get("end", 0))
            text = segment.get("text", "").strip()
            
            vtt_content.append(f"{start_time} --> {end_time}")
            vtt_content.append(text)
            vtt_content.append("")
        
        return "\n".join(vtt_content)
    
    def _format_time_srt(self, seconds: float) -> str:
        """Format time for SRT format (HH:MM:SS,mmm)."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def _format_time_vtt(self, seconds: float) -> str:
        """Format time for VTT format (HH:MM:SS.mmm)."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}.{millisecs:03d}"


class Transcriptions:
    """Transcriptions interface that mimics OpenAI's audio.transcriptions."""
    
    def __init__(self, client: 'STTClient'):
        self._client = client
    
    def create(
        self,
        *,
        model: str,
        file: Union[BinaryIO, str, Path],
        language: Optional[str] = None,
        prompt: Optional[str] = None,
        response_format: str = "json",
        temperature: Optional[float] = None,
        timestamp_granularities: Optional[list] = None,
        **kwargs
    ) -> TranscriptionResponse:
        """
        Create a transcription of the given audio file.
        
        Args:
            model (str): Model to use for transcription (e.g., "whisper-1")
            file (Union[BinaryIO, str, Path]): Audio file to transcribe
            language (Optional[str]): Language of the audio (ISO-639-1 format)
            prompt (Optional[str]): Optional text to guide the model's style
            response_format (str): Format of the response ("json", "text", "srt", "verbose_json", "vtt")
            temperature (Optional[float]): Sampling temperature (0 to 1)
            timestamp_granularities (Optional[list]): Timestamp granularities to include
            **kwargs: Additional parameters
            
        Returns:
            TranscriptionResponse: Transcription result
        """
        # Handle file input
        if isinstance(file, (str, Path)):
            file_path = Path(file)
        elif hasattr(file, 'read'):
            # Handle file-like objects
            file_path = self._save_temp_file(file)
        else:
            raise ValueError("File must be a path string, Path object, or file-like object")
        
        # Get the appropriate provider based on model
        provider = self._client._get_provider(model)
        
        # Prepare transcription parameters
        transcribe_kwargs = {}
        if language:
            transcribe_kwargs['language'] = language
        if prompt:
            transcribe_kwargs['prompt'] = prompt
        if temperature is not None:
            transcribe_kwargs['temperature'] = temperature
        if timestamp_granularities:
            transcribe_kwargs['timestamp_granularities'] = timestamp_granularities
        
        # Add any additional kwargs
        transcribe_kwargs.update(kwargs)
        
        # Perform transcription
        result = provider.transcribe(file_path, **transcribe_kwargs)
        
        # Clean up temporary file if created
        if hasattr(file, 'read') and file_path.exists():
            file_path.unlink(missing_ok=True)
        
        return TranscriptionResponse(result, response_format)
    
    def _save_temp_file(self, file_obj: BinaryIO) -> Path:
        """Save file-like object to temporary file."""
        import tempfile
        import os
        
        # Read the file content
        content = file_obj.read()
        
        # Create temporary file
        temp_fd, temp_path = tempfile.mkstemp(suffix='.audio')
        try:
            with os.fdopen(temp_fd, 'wb') as temp_file:
                temp_file.write(content)
        except:
            os.close(temp_fd)
            raise
        
        return Path(temp_path)


class Audio:
    """Audio interface that mimics OpenAI's audio namespace."""
    
    def __init__(self, client: 'STTClient'):
        self.transcriptions = Transcriptions(client)


class STTClient:
    """
    OpenAI-compatible STT client for Webscout.
    
    This client provides an interface that matches the OpenAI Whisper API,
    allowing for easy migration and compatibility with existing OpenAI code.
    
    Usage:
        client = STTClient()
        audio_file = open("/path/to/file/audio.mp3", "rb")
        transcription = client.audio.transcriptions.create(
            model="whisper-1",
            file=audio_file,
            response_format="text"
        )
        print(transcription.text)
    """
    
    AVAILABLE_MODELS = [
        "whisper-1",
        "gpt-4o-transcribe",
        "elevenlabs-scribe",
        "elevenlabs-scribe-v1"
    ]
    
    def __init__(
        self,
        default_provider: str = "elevenlabs",
        timeout: int = 60,
        **kwargs
    ):
        """
        Initialize the STT client.
        
        Args:
            default_provider (str): Default STT provider to use
            timeout (int): Request timeout in seconds
            **kwargs: Additional parameters for providers
        """
        self.default_provider = default_provider
        self.timeout = timeout
        self.provider_kwargs = kwargs
        self.audio = Audio(self)
        
        # Initialize providers
        self._providers = {}
        self._init_providers()
    
    def _init_providers(self):
        """Initialize available STT providers."""
        try:
            self._providers['elevenlabs'] = ElevenLabsSTT(timeout=self.timeout, **self.provider_kwargs)
        except Exception as e:
            print(f"Warning: Could not initialize ElevenLabs provider: {e}")
    
    def _get_provider(self, model: str) -> BaseSTTProvider:
        """Get the appropriate provider for the given model."""
        if model in ["whisper-1", "gpt-4o-transcribe"]:
            # For OpenAI models, use ElevenLabs as fallback
            provider_name = "elevenlabs"
        elif model.startswith("elevenlabs"):
            provider_name = "elevenlabs"
        else:
            # Default to the configured default provider
            provider_name = self.default_provider
        
        if provider_name not in self._providers:
            raise ValueError(f"Provider '{provider_name}' not available")
        
        return self._providers[provider_name]


# Convenience alias for backward compatibility
OpenAISTT = STTClient

__all__ = ['STTClient', 'OpenAISTT', 'TranscriptionResponse', 'Transcriptions', 'Audio']
